# CETO Chat Application

The CETO Chat application provides a Vue.js-based chat interface with support for both regular conversational AI and real-time streaming for long-running MCP (Model Context Protocol) tool operations.

## Architecture Overview

The chat application uses a dual-path architecture to handle different types of user interactions:

1. **Regular Chat Path**: For standard conversational messages
2. **Streaming Chat Path**: For direct MCP tool calls that require real-time progress updates

## Chat Workflow Decision Tree

```
User sends message
       ↓
Is provider MCP/MCP-HTTP?
       ↓
    No ─────→ Use Regular Chat Path
       ↓
    Yes
       ↓
Is message a direct tool call?
       ↓
    No ─────→ Use Regular Chat Path
       ↓
    Yes
       ↓
Use Streaming Chat Path
```

## Direct Tool Call Detection

A message is considered a **direct tool call** if it matches exactly one of the predefined tool names:

### Frontend Detection (`chat_app.html`)
```javascript
isDirectToolCall(message) {
    const trimmed = message.trim();
    const directToolCalls = [
        'long_task',
        'echostring_table',
        'firecrawl_scrape',
        'firecrawl_scrape_text_only'
    ];
    return directToolCalls.includes(trimmed);
}
```

### Backend Detection (`api.py`)
```python
def is_direct_tool_call(message):
    """Check if message is a direct tool call."""
    message = message.strip()
    direct_tools = ['long_task', 'echostring_table', 'firecrawl_scrape', 'firecrawl_scrape_text_only']
    return message in direct_tools, message if message in direct_tools else None
```

## Regular Chat Path

**Used for**: Standard conversational messages, complex queries, non-direct tool calls

### Frontend Flow
1. `sendMessage()` → `sendRegularMessage()`
2. POST to `/gaia_chat/api/chat/`
3. Synchronous response handling
4. Immediate UI update

### Backend Flow
1. `send_message()` endpoint in `api.py`
2. `ChatManager.process_message()` 
3. LLM processing (OpenAI, Anthropic, Mock, or MCP)
4. JSON response with final result

### Response Format
```json
{
    "message": "Assistant response content",
    "conversation_id": "uuid",
    "success": true
}
```

## Streaming Chat Path

**Used for**: Direct MCP tool calls that support real-time progress updates

### Frontend Flow
1. `sendMessage()` → `sendStreamingMessage()`
2. POST to `/gaia_chat/api/messages/send-stream/`
3. Server-Sent Events (SSE) stream processing
4. Real-time UI updates via `handleStreamingData()`

### Backend Flow
1. `send_message_stream()` endpoint in `api.py`
2. Early filtering: redirect non-direct tool calls to regular path
3. `handle_direct_tool_call_with_progress()` with FastMCP
4. Async generator yielding SSE events

### SSE Event Types

#### Progress Events
```json
{
    "type": "progress",
    "progress": 3,
    "total": 10,
    "percentage": 30.0,
    "bar": "███░░░░░░░",
    "message": "Processing step 3",
    "console_message": "📊 [███░░░░░░░] 30.0% 3/10 – Processing step 3"
}
```

#### Info Events
```json
{
    "type": "info",
    "content": "🔍 Detected direct tool call: long_task"
}
```

#### Result Events
```json
{
    "type": "result", 
    "content": "Tool execution result data"
}
```

#### Final Events
```json
{
    "type": "final",
    "content": "Task completed successfully with result"
}
```

#### Completion Events
```json
{
    "type": "complete"
}
```

## Provider Support

### MCP Providers
- **mcp**: SSE-based MCP server connection
- **mcp-http**: HTTP-based MCP server connection
- Support both regular chat and streaming for direct tool calls

### Non-MCP Providers  
- **openai**: OpenAI API
- **anthropic**: Anthropic API
- **mock**: Mock LLM for testing
- Only support regular chat path

## Error Handling

### Event Loop Conflicts
The dual-path architecture prevents "Cannot run the event loop while another loop is running" errors by:
- Routing regular messages through synchronous endpoints
- Only using async streaming for direct tool calls
- Early filtering in `send_message_stream()` to redirect inappropriate requests

### Timeout Management
- MCP client timeout: 70 seconds (supports 60-second long-running tasks)
- Frontend streaming timeout: Handled by browser SSE implementation
- Fallback to regular chat on streaming failures

## Console Logging

### Frontend Console Output
Real-time progress messages are logged to browser console in the same format as `chat_term.py`:
```
📊 [███░░░░░░░] 30.0% 3/10 – Processing step 3
🔍 Detected direct tool call: long_task
✅ Task completed successfully
```

### Backend Console Output  
Progress messages are also logged to Django runserver stdout for debugging.

## Available MCP Tools

### Web Scraping Tools

#### `firecrawl_scrape`
Standard web scraping tool that returns complete page content including images, media, and full HTML structure.

**Usage**:
- Direct call: `firecrawl_scrape`
- In conversation: "Please scrape https://example.com using firecrawl"

**Output**: Full markdown content with images, media, and complete page structure.

#### `firecrawl_scrape_text_only`
Lightweight web scraping tool optimized for text content extraction. Automatically filters out:
- Markdown image references (`![alt](url)`)
- HTML image tags (`<img>`)
- Iframe elements and references
- Base64-encoded images

**Usage**:
- Direct call: `firecrawl_scrape_text_only`
- In conversation: "Please scrape https://example.com using the text-only version"

**Output**: Clean text content without images or media elements.

**Benefits**:
- Faster processing and smaller responses
- Reduced token usage for LLM processing
- Focused on readable text content
- Eliminates image bloat in responses

### Progress Reporting Tools

#### `long_task`
Demonstration tool for testing long-running operations with real-time progress updates.

#### `echostring_table`
Returns structured table data in JSON format, automatically formatted as HTML tables in the frontend.



## Configuration

### Adding New Direct Tool Calls
To add support for a new direct tool call:

1. **Frontend**: Add tool name to `directToolCalls` array in `isDirectToolCall()`
2. **Backend**: Add tool name to `direct_tools` list in `is_direct_tool_call()`
3. **MCP Server**: Implement the tool with progress reporting support

### Timeout Settings
```python
# In MCP client configuration
timeout_seconds = 70  # Supports 60-second tasks + overhead
```

## Testing

### Direct Tool Call Testing
```bash
# Test direct tool calls
echo "long_task" | python chat_term.py --provider mcp-http
echo "firecrawl_scrape_text_only" | python chat_term.py --provider mcp-http

# Test regular chat
echo "How old is the Sun?" | python chat_term.py --provider mcp-http
```

### Frontend Testing
1. Open browser to `/gaia_chat/`
2. Select MCP provider
3. Test direct tool call: `long_task`
4. Test regular message: `How old is the Sun?`
5. Verify appropriate path usage in browser console

## Troubleshooting

### No Response in Frontend
- Check if message is incorrectly routed to streaming path
- Verify provider selection (MCP vs non-MCP)
- Check browser console for JavaScript errors

### Streaming Not Working
- Verify message is detected as direct tool call
- Check MCP server availability
- Verify FastMCP integration is working

### Event Loop Errors
- Indicates regular message incorrectly using streaming path
- Check `isDirectToolCall()` logic
- Verify early filtering in `send_message_stream()`

## API Endpoints

### Regular Chat Endpoint
```
POST /gaia_chat/api/chat/
Content-Type: application/json

{
    "conversation_id": "uuid",
    "messages": [...],
    "provider": "openai|anthropic|mock|mcp|mcp-http"
}

Response:
{
    "message": "Assistant response",
    "conversation_id": "uuid",
    "success": true
}
```

### Streaming Chat Endpoint
```
POST /gaia_chat/api/messages/send-stream/
Content-Type: application/json

{
    "message": "long_task",
    "conversation_id": "uuid",
    "provider": "mcp|mcp-http"
}

Response: text/plain (Server-Sent Events)
data: {"type": "info", "content": "Starting task..."}
data: {"type": "progress", "progress": 1, "total": 10, ...}
data: {"type": "final", "content": "Result"}
data: {"type": "complete"}
```

## Implementation Details

### FastMCP Integration
The streaming path uses FastMCP for real-time progress reporting:

```python
# Progress tracking with context injection
async def handle_direct_tool_call_with_progress(tool_name, server_url, tracker):
    async with get_mcp_client(server_url, timeout=70) as client:
        # Call tool with progress token
        result = await client.call_tool(
            tool_name,
            arguments={},
            progress_token=tracker.progress_token
        )

        # Yield progress events as they arrive
        async for event in tracker.get_events():
            yield f"data: {json.dumps(event)}\n\n"
```

### Progress Message Format
Progress messages maintain consistency between terminal and web interfaces:

**Terminal Output (`chat_term.py`)**:
```
📊 [███░░░░░░░] 30.0% 3/10 – Processing step 3
```

**Browser Console**:
```
📊 [███░░░░░░░] 30.0% 3/10 – Processing step 3
```

**Web UI Display**:
```
🚀 Task in progress...

📊 [█░░░░░░░░░] 10.0% 1/10 – Starting task
📊 [██░░░░░░░░] 20.0% 2/10 – Processing data
📊 [███░░░░░░░] 30.0% 3/10 – Processing step 3
```

## Security Considerations

### CSRF Protection
All POST requests include CSRF tokens:
```javascript
headers: {
    'X-CSRFToken': this.getCsrfToken()
}
```

### User Isolation
- Conversations are filtered by authenticated user
- No cross-user conversation access
- Proper Django authentication integration

### Input Validation
- Message content sanitization
- Provider validation against allowed list
- Conversation ID validation

## Performance Considerations

### Memory Management
- Streaming responses use async generators to avoid memory buildup
- Progress events are yielded immediately, not buffered
- Client-side progress arrays are reset between calls

### Connection Management
- MCP client connections are properly closed after use
- SSE connections auto-terminate on completion or error
- Timeout handling prevents hanging connections

### Scalability
- Regular chat path is stateless and scales horizontally
- Streaming path maintains minimal server state
- Progress tracking uses lightweight in-memory structures

## Future Enhancements

### Planned Features
1. **Dynamic Tool Discovery**: Auto-detect available MCP tools
2. **Tool Parameter Support**: Handle tools with arguments
3. **Multi-step Workflows**: Chain multiple tool calls
4. **Progress Persistence**: Save progress state across sessions
5. **Tool Result Caching**: Cache expensive tool results

### Integration Opportunities
1. **WebSocket Support**: Alternative to SSE for bidirectional communication
2. **Tool Marketplace**: Plugin system for third-party tools
3. **Advanced Progress Visualization**: Charts and graphs for complex operations
4. **Collaborative Features**: Shared conversations and tool results
