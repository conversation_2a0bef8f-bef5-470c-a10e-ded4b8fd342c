{"version": "1.0", "description": "MCP Server Registry for CETO Chat Terminal", "default_server": "gaia_local_sse", "servers": {"gaia_local_sse": {"name": "Gaia Local SSE Server", "description": "Local Gaia MCP server using SSE protocol", "protocol": "sse", "url": "http://0.0.0.0:9000/sse", "type": "builtin", "health_check_path": "/health", "tools": ["echostring", "echostring_table", "echostring_process_items", "echostring_longrunning", "echostring_streaming_progress", "get_company_categories_matching", "get_llm_completion", "get_top_companies", "get_company_listing"], "model": "claude-3-5-sonnet-20240620"}, "gaia_local_http": {"name": "Gaia Local HTTP Server", "description": "Local Gaia MCP server using HTTP protocol", "protocol": "http", "url": "http://0.0.0.0:9000/mcp", "type": "builtin", "health_check_path": "/health", "tools": ["echostring", "echostring_table", "long_task", "firecrawl_scrape", "firecrawl_scrape_text_only"], "model": "claude-3-5-sonnet-20240620"}, "gaia_demo": {"name": "Gaia Demo Server", "description": "Demo MCP server with basic tools", "protocol": "http", "url": "http://0.0.0.0:8001/mcp", "type": "builtin", "health_check_path": "/health", "tools": ["get_notes"], "model": "claude-3-5-sonnet-20240620"}, "progress_test": {"name": "Progress Test Server", "description": "Test server for progress reporting", "protocol": "http", "url": "http://127.0.0.1:9000/mcp", "type": "builtin", "health_check_path": "/health", "tools": ["long_task"], "model": "claude-3-5-sonnet-20240620"}}, "third_party_examples": {"anthropic_computer_use": {"name": "Anthropic Computer Use", "description": "<PERSON>throp<PERSON>'s computer use MCP server", "protocol": "stdio", "command": "npx", "args": ["@anthropic-ai/mcp-server-computer-use"], "type": "third_party", "tools": ["computer_use"], "model": "claude-3-5-sonnet-20240620", "documentation": "https://github.com/anthropics/anthropic-quickstarts/tree/main/computer-use-demo"}, "filesystem": {"name": "Filesystem MCP Server", "description": "MCP server for filesystem operations", "protocol": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "/path/to/allowed/directory"], "type": "third_party", "tools": ["read_file", "write_file", "create_directory", "list_directory"], "model": "claude-3-5-sonnet-20240620", "documentation": "https://github.com/modelcontextprotocol/servers/tree/main/src/filesystem"}, "brave_search": {"name": "Brave Search MCP Server", "description": "MCP server for Brave Search API", "protocol": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-brave-search"], "type": "third_party", "environment": {"BRAVE_API_KEY": "required"}, "tools": ["brave_web_search"], "model": "claude-3-5-sonnet-20240620", "documentation": "https://github.com/modelcontextprotocol/servers/tree/main/src/brave-search"}, "github": {"name": "GitHub MCP Server", "description": "MCP server for GitHub API operations", "protocol": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-github"], "type": "third_party", "environment": {"GITHUB_PERSONAL_ACCESS_TOKEN": "required"}, "tools": ["create_repository", "get_repository", "list_repositories", "create_issue", "get_issue", "list_issues"], "model": "claude-3-5-sonnet-20240620", "documentation": "https://github.com/modelcontextprotocol/servers/tree/main/src/github"}, "postgres": {"name": "PostgreSQL MCP Server", "description": "MCP server for PostgreSQL database operations", "protocol": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-postgres"], "type": "third_party", "environment": {"POSTGRES_CONNECTION_STRING": "required"}, "tools": ["read_query", "write_query", "create_table", "list_tables", "describe_table"], "model": "claude-3-5-sonnet-20240620", "documentation": "https://github.com/modelcontextprotocol/servers/tree/main/src/postgres"}}}