#!/usr/bin/env python3
"""
Command-line script to call long_task tool directly.

Usage:
    python call_long_task.py                           # Use default MCP HTTP server
    python call_long_task.py --server http://localhost:9000/mcp  # Custom server
    python call_long_task.py --llm mock                # Use MockLLM instead
    python call_long_task.py --context companies.json  # Load context first
"""

import argparse
import tempfile
import os
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from chat_term import ChatTerminal


def main():
    """Main function to call long_task from command line."""
    parser = argparse.ArgumentParser(description="Call long_task tool from command line")
    parser.add_argument(
        "--server", 
        default="http://0.0.0.0:9000/mcp",
        help="MCP server URL (default: http://0.0.0.0:9000/mcp)"
    )
    parser.add_argument(
        "--llm",
        choices=["mcp-http", "mcp", "mock", "openai", "anthropic"],
        default="mcp-http",
        help="LLM provider to use (default: mcp-http)"
    )
    parser.add_argument(
        "--context",
        help="Context JSON file to load before calling long_task"
    )
    parser.add_argument(
        "--storage-dir",
        help="Directory to store conversations (default: temporary directory)"
    )
    parser.add_argument(
        "--user-id",
        default="cli_user",
        help="User ID for the conversation (default: cli_user)"
    )
    parser.add_argument(
        "--title",
        default="CLI Long Task",
        help="Title for the conversation (default: CLI Long Task)"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output"
    )
    
    args = parser.parse_args()
    
    # Set up storage directory
    if args.storage_dir:
        storage_dir = args.storage_dir
    else:
        storage_dir = tempfile.mkdtemp()
        if args.verbose:
            print(f"📁 Using temporary storage directory: {storage_dir}")
    
    # Determine model name based on LLM provider
    model_name = None
    if args.llm in ["mcp-http", "mcp"]:
        model_name = args.server
    
    print("🚀 Initializing Chat Terminal...")
    print(f"   LLM Provider: {args.llm}")
    if model_name:
        print(f"   Server URL: {model_name}")
    print(f"   User ID: {args.user_id}")
    print(f"   Storage: {storage_dir}")
    
    try:
        # Create ChatTerminal instance
        terminal = ChatTerminal(
            storage_dir=storage_dir,
            llm_provider=args.llm,
            model_name=model_name,
            user_id=args.user_id
        )
        
        # Create a new conversation
        print(f"\n📝 Creating conversation: {args.title}")
        terminal.cmd_new(args.title)
        
        # Load context if specified
        if args.context:
            print(f"\n📂 Loading context from: {args.context}")
            # Set context directory to current directory
            terminal.context_dir = os.getcwd()
            terminal.cmd_load_context(args.context)
        
        # Call long_task
        print("\n🔧 Calling long_task tool...")
        print("=" * 60)
        
        terminal.handle_message("long_task")
        
        print("=" * 60)
        print("✅ long_task completed successfully!")
        
        # Show conversation info
        if args.verbose and terminal.chat_manager.active_conversation:
            conv = terminal.chat_manager.active_conversation
            print(f"\n📊 Conversation Summary:")
            print(f"   ID: {conv.conversation_id}")
            print(f"   Messages: {len(conv.messages)}")
            print(f"   Last updated: {conv.updated_at}")
            
            # Show the last assistant message
            if conv.messages:
                last_msg = conv.messages[-1]
                if last_msg['role'] == 'assistant':
                    content = last_msg['content']
                    if len(content) > 200:
                        content = content[:197] + "..."
                    print(f"   Last response: {content}")
        
    except KeyboardInterrupt:
        print("\n⚠️  Interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)
    
    print(f"\n🎉 Command completed successfully!")


if __name__ == "__main__":
    main()
