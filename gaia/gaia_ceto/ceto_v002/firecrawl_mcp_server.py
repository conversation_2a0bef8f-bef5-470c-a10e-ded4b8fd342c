#!/usr/bin/env python3
"""
Standalone Firecrawl MCP Server

This is a dedicated MCP server that provides Firecrawl web scraping tools.
It can run independently and be configured as a third-party server in the MCP registry.

Features:
- Firecrawl web scraping with full content
- Text-only scraping for LLM-friendly content
- Batch URL processing
- Health check endpoint
- Environment variable configuration

Usage:
    python firecrawl_mcp_server.py --port 9001
    python firecrawl_mcp_server.py --port 9001 --protocol http
    python firecrawl_mcp_server.py --port 9001 --protocol sse
"""

import os
import sys
import asyncio
import argparse
import uvicorn
from typing import List, Optional
from pydantic import Field

# Add the parent directory to the path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from mcp.server.fastmcp import FastMCP, Context
except ImportError:
    print("Error: FastMCP not available. Please install the MCP server library.")
    sys.exit(1)

try:
    from firecrawl import FirecrawlApp
except ImportError:
    print("Error: Firecrawl library not available. Please install with: pip install firecrawl-py")
    sys.exit(1)

# Initialize FastMCP server
mcp = FastMCP("firecrawl_mcp_server")

# Global Firecrawl app instance
firecrawl_app = None


def init_firecrawl():
    """Initialize the Firecrawl app with API key from environment."""
    global firecrawl_app
    
    api_key = os.environ.get('FIRECRAWL_API_KEY')
    if not api_key:
        print("Warning: FIRECRAWL_API_KEY environment variable not set.")
        print("Please set it with: export FIRECRAWL_API_KEY=your_api_key")
        print("You can get an API key from: https://firecrawl.dev")
        return False
    
    try:
        firecrawl_app = FirecrawlApp(api_key=api_key)
        print(f"✅ Firecrawl initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to initialize Firecrawl: {e}")
        return False


@mcp.tool()
async def firecrawl_scrape(
    url: str = Field(description="The URL to scrape"),
    include_html: bool = Field(default=False, description="Whether to include HTML content"),
    include_raw_html: bool = Field(default=False, description="Whether to include raw HTML"),
    include_screenshot: bool = Field(default=False, description="Whether to include screenshot"),
    wait_for: int = Field(default=0, description="Time to wait for dynamic content (milliseconds)"),
    timeout: int = Field(default=30000, description="Request timeout in milliseconds")
) -> str:
    """Scrape a URL and return the content in markdown format with optional extras.

    This tool provides comprehensive web scraping with full control over output formats.
    Use this when you need complete page content including images, media, and structure.

    Args:
        url: The URL to scrape
        include_html: Include cleaned HTML in the response
        include_raw_html: Include raw HTML in the response
        include_screenshot: Include a screenshot of the page
        wait_for: Time to wait for dynamic content to load (in milliseconds)
        timeout: Request timeout in milliseconds (default: 30 seconds)
    """
    if not firecrawl_app:
        return "❌ Firecrawl not initialized. Please check FIRECRAWL_API_KEY environment variable."
    
    try:
        # Build formats list
        formats = ["markdown"]
        if include_html:
            formats.append("html")
        if include_raw_html:
            formats.append("rawHtml")
        if include_screenshot:
            formats.append("screenshot")
        
        # Build scrape options
        scrape_options = {
            "formats": formats,
            "timeout": timeout
        }
        
        if wait_for > 0:
            scrape_options["waitFor"] = wait_for
        
        print(f"🔍 Scraping URL: {url}")
        print(f"📋 Formats: {', '.join(formats)}")
        
        scrape_response = firecrawl_app.scrape_url(url, **scrape_options)
        
        # Build response
        result = f"# Scraped Content from {url}\n\n"
        
        if hasattr(scrape_response, 'markdown') and scrape_response.markdown:
            result += "## Markdown Content\n\n"
            result += scrape_response.markdown
            result += "\n\n"
        
        if include_html and hasattr(scrape_response, 'html') and scrape_response.html:
            result += "## HTML Content\n\n"
            result += f"```html\n{scrape_response.html}\n```\n\n"
        
        if include_raw_html and hasattr(scrape_response, 'rawHtml') and scrape_response.rawHtml:
            result += "## Raw HTML Content\n\n"
            result += f"```html\n{scrape_response.rawHtml[:2000]}...\n```\n\n"
        
        if include_screenshot and hasattr(scrape_response, 'screenshot'):
            result += "## Screenshot\n\n"
            result += f"Screenshot captured: {len(scrape_response.screenshot) if scrape_response.screenshot else 0} bytes\n\n"
        
        print(f"✅ Successfully scraped {url}")
        return result
        
    except Exception as e:
        error_msg = f"❌ Error scraping {url}: {str(e)}"
        print(error_msg)
        return error_msg


@mcp.tool()
async def firecrawl_scrape_text_only(
    url: str = Field(description="The URL to scrape"),
    wait_for: int = Field(default=0, description="Time to wait for dynamic content (milliseconds)"),
    timeout: int = Field(default=30000, description="Request timeout in milliseconds")
) -> str:
    """Scrape a URL and return only the main text content without images or media.

    This is a lightweight version optimized for LLM processing. It automatically:
    - Extracts only main content (excludes navigation, footers, ads)
    - Removes images, videos, and media elements
    - Filters out base64-encoded images
    - Returns clean markdown suitable for text analysis

    Args:
        url: The URL to scrape
        wait_for: Time to wait for dynamic content to load (in milliseconds)
        timeout: Request timeout in milliseconds (default: 30 seconds)
    """
    if not firecrawl_app:
        return "❌ Firecrawl not initialized. Please check FIRECRAWL_API_KEY environment variable."
    
    try:
        # Build scrape options for text-only extraction
        scrape_options = {
            "formats": ["markdown"],
            "onlyMainContent": True,
            "removeBase64Images": True,
            "timeout": timeout
        }
        
        if wait_for > 0:
            scrape_options["waitFor"] = wait_for
        
        print(f"🔍 Scraping URL (text-only): {url}")
        
        scrape_response = firecrawl_app.scrape_url(url, **scrape_options)
        
        if hasattr(scrape_response, 'markdown') and scrape_response.markdown:
            # Additional text cleaning for LLM consumption
            content = scrape_response.markdown
            
            # Remove remaining image references
            import re
            content = re.sub(r'!\[.*?\]\(.*?\)', '', content)  # Remove ![alt](url)
            content = re.sub(r'<img[^>]*>', '', content)       # Remove <img> tags
            content = re.sub(r'<iframe[^>]*>.*?</iframe>', '', content, flags=re.DOTALL)  # Remove iframes
            
            # Clean up extra whitespace
            content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)  # Multiple newlines to double
            content = content.strip()
            
            result = f"# Text Content from {url}\n\n{content}"
            print(f"✅ Successfully scraped text from {url} ({len(content)} characters)")
            return result
        else:
            error_msg = f"❌ No content found at {url}"
            print(error_msg)
            return error_msg
            
    except Exception as e:
        error_msg = f"❌ Error scraping {url}: {str(e)}"
        print(error_msg)
        return error_msg


@mcp.tool()
async def firecrawl_batch_scrape(
    urls: List[str] = Field(description="List of URLs to scrape"),
    text_only: bool = Field(default=True, description="Whether to use text-only mode"),
    max_concurrent: int = Field(default=3, description="Maximum concurrent requests")
) -> str:
    """Scrape multiple URLs concurrently and return combined results.

    Args:
        urls: List of URLs to scrape
        text_only: Whether to use text-only mode (recommended for multiple URLs)
        max_concurrent: Maximum number of concurrent requests (default: 3)
    """
    if not firecrawl_app:
        return "❌ Firecrawl not initialized. Please check FIRECRAWL_API_KEY environment variable."
    
    if not urls:
        return "❌ No URLs provided"
    
    if len(urls) > 10:
        return "❌ Maximum 10 URLs allowed per batch request"
    
    print(f"🔍 Batch scraping {len(urls)} URLs (text_only={text_only})")
    
    results = []
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def scrape_single(url: str) -> str:
        async with semaphore:
            if text_only:
                return await firecrawl_scrape_text_only(url)
            else:
                return await firecrawl_scrape(url)
    
    try:
        # Execute all scraping tasks concurrently
        tasks = [scrape_single(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Combine results
        combined_result = f"# Batch Scraping Results ({len(urls)} URLs)\n\n"
        
        for i, (url, result) in enumerate(zip(urls, results), 1):
            combined_result += f"## Result {i}: {url}\n\n"
            
            if isinstance(result, Exception):
                combined_result += f"❌ Error: {str(result)}\n\n"
            else:
                combined_result += f"{result}\n\n"
            
            combined_result += "---\n\n"
        
        print(f"✅ Batch scraping completed: {len(urls)} URLs processed")
        return combined_result
        
    except Exception as e:
        error_msg = f"❌ Batch scraping failed: {str(e)}"
        print(error_msg)
        return error_msg


@mcp.tool()
async def firecrawl_health_check() -> str:
    """Check the health status of the Firecrawl service and API key."""
    if not firecrawl_app:
        return "❌ Firecrawl not initialized. Please check FIRECRAWL_API_KEY environment variable."
    
    try:
        # Try a simple scrape to test the service
        test_url = "https://httpbin.org/json"
        scrape_response = firecrawl_app.scrape_url(
            test_url, 
            formats=["markdown"],
            timeout=10000
        )
        
        if hasattr(scrape_response, 'markdown') and scrape_response.markdown:
            return "✅ Firecrawl service is healthy and API key is valid"
        else:
            return "⚠️ Firecrawl service responded but no content returned"
            
    except Exception as e:
        return f"❌ Firecrawl health check failed: {str(e)}"


def create_app(protocol: str = "http"):
    """Create the FastAPI app for the specified protocol."""
    if protocol == "sse":
        return mcp.sse_app()
    elif protocol == "http":
        return mcp.streamable_http_app()
    else:
        raise ValueError(f"Unsupported protocol: {protocol}")


def main():
    """Main entry point for the Firecrawl MCP server."""
    parser = argparse.ArgumentParser(description="Standalone Firecrawl MCP Server")
    parser.add_argument(
        "--port", 
        type=int, 
        default=9001, 
        help="Port to run the server on (default: 9001)"
    )
    parser.add_argument(
        "--protocol",
        choices=["http", "sse"],
        default="http",
        help="Protocol to use: http or sse (default: http)"
    )
    parser.add_argument(
        "--host",
        default="0.0.0.0",
        help="Host to bind to (default: 0.0.0.0)"
    )
    parser.add_argument(
        "--log-level",
        choices=["debug", "info", "warning", "error"],
        default="info",
        help="Log level (default: info)"
    )
    
    args = parser.parse_args()
    
    print("🚀 Starting Firecrawl MCP Server")
    print(f"   Protocol: {args.protocol}")
    print(f"   Host: {args.host}")
    print(f"   Port: {args.port}")
    print(f"   Log Level: {args.log_level}")
    
    # Initialize Firecrawl
    if not init_firecrawl():
        print("❌ Failed to initialize Firecrawl. Server will start but tools will not work.")
        print("   Please set FIRECRAWL_API_KEY environment variable and restart.")
    
    # Create the app
    try:
        app = create_app(args.protocol)
        print(f"✅ Created {args.protocol.upper()} app")
        
        # Add health check endpoint
        if hasattr(app, 'get'):
            @app.get("/health")
            async def health():
                return {"status": "healthy", "service": "firecrawl_mcp_server"}
        
        print(f"🌐 Server starting at http://{args.host}:{args.port}")
        print(f"📋 Available tools: firecrawl_scrape, firecrawl_scrape_text_only, firecrawl_batch_scrape, firecrawl_health_check")
        print("🔑 Make sure FIRECRAWL_API_KEY environment variable is set")
        
        # Run the server
        uvicorn.run(
            app, 
            host=args.host, 
            port=args.port, 
            log_level=args.log_level
        )
        
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
