#!/usr/bin/env python3
"""
Test script for Firecrawl hosted MCP server integration.

This script tests connecting to the Firecrawl hosted MCP endpoint using
the URL template with API key substitution.

Usage:
    export FIRECRAWL_API_KEY=your_api_key
    python test_firecrawl_hosted.py
"""

import asyncio
import os
import sys
from typing import Optional

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from mcp_server_registry import MCPServerRegistry, MCPServerInfo
from mcp_provider import UnifiedMCPProvider


async def test_firecrawl_hosted():
    """Test connecting to the Firecrawl hosted MCP server."""
    print("🧪 Testing Firecrawl Hosted MCP Server Integration")
    print("=" * 60)
    
    # Check if API key is set
    api_key = os.environ.get('FIRECRAWL_API_KEY')
    if not api_key:
        print("❌ FIRECRAWL_API_KEY environment variable not set")
        print("   Please set it with: export FIRECRAWL_API_KEY=your_api_key")
        print("   You can get an API key from: https://firecrawl.dev/app/api-keys")
        return False
    
    print(f"✅ FIRECRAWL_API_KEY found: {api_key[:8]}...")
    
    # Load the server registry
    print("\n📋 Loading MCP server registry...")
    try:
        registry = MCPServerRegistry()
        servers = registry.list_servers()
        print(f"✅ Loaded {len(servers)} servers from registry")
        
        # Find the Firecrawl hosted server
        firecrawl_server = registry.get_server("example_firecrawl_hosted")
        if not firecrawl_server:
            print("❌ Firecrawl hosted server not found in registry")
            print("   Available servers:")
            for server_id, server_info in registry.servers.items():
                print(f"     - {server_id}: {server_info.name}")
            return False
        
        print(f"✅ Found Firecrawl server: {firecrawl_server.name}")
        print(f"   Protocol: {firecrawl_server.protocol}")
        print(f"   URL template: {firecrawl_server.url}")
        
        # Test URL resolution
        resolved_url = firecrawl_server.get_resolved_url()
        if resolved_url:
            print(f"✅ URL resolved successfully")
            print(f"   Resolved URL: {resolved_url[:50]}...")
        else:
            print("❌ Failed to resolve URL")
            return False
        
    except Exception as e:
        print(f"❌ Error loading registry: {e}")
        return False
    
    # Test connection using UnifiedMCPProvider
    print("\n🔌 Testing connection to Firecrawl hosted server...")
    try:
        provider = UnifiedMCPProvider(registry)
        
        # Try to connect to the Firecrawl server
        success = await provider.connect_to_server("example_firecrawl_hosted")
        
        if success:
            print("✅ Successfully connected to Firecrawl hosted server!")
            
            # List available tools
            tools = provider.get_available_tools()
            if tools:
                print(f"📋 Available tools ({len(tools)}):")
                for tool in tools[:5]:  # Show first 5 tools
                    print(f"   - {tool}")
                if len(tools) > 5:
                    print(f"   ... and {len(tools) - 5} more")
            else:
                print("⚠️  No tools discovered")
            
            # Test a simple tool call
            print("\n🔧 Testing firecrawl_scrape tool...")
            try:
                result = await provider.call_tool(
                    "firecrawl_scrape",
                    {
                        "url": "https://httpbin.org/json",
                        "formats": ["markdown"],
                        "onlyMainContent": True
                    }
                )
                
                if result:
                    print("✅ Tool call successful!")
                    # Show first 200 characters of result
                    result_str = str(result)
                    if len(result_str) > 200:
                        result_str = result_str[:197] + "..."
                    print(f"   Result preview: {result_str}")
                else:
                    print("⚠️  Tool call returned empty result")
                    
            except Exception as e:
                print(f"❌ Tool call failed: {e}")
        
        else:
            print("❌ Failed to connect to Firecrawl hosted server")
            return False
        
        # Clean up
        await provider.cleanup()
        
    except Exception as e:
        print(f"❌ Error testing connection: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n🎉 Firecrawl hosted MCP server test completed successfully!")
    return True


async def test_url_resolution():
    """Test URL template resolution with different scenarios."""
    print("\n🧪 Testing URL Template Resolution")
    print("-" * 40)
    
    # Test case 1: Valid API key
    test_server = MCPServerInfo(
        name="Test Server",
        description="Test server for URL resolution",
        protocol="sse",
        url="https://mcp.firecrawl.dev/{FIRECRAWL_API_KEY}/sse",
        type="third_party",
        api_key_template="https://mcp.firecrawl.dev/{FIRECRAWL_API_KEY}/sse"
    )
    
    resolved = test_server.get_resolved_url()
    if resolved:
        print(f"✅ URL resolution successful: {resolved[:50]}...")
    else:
        print("❌ URL resolution failed")
    
    # Test case 2: Missing API key
    os.environ.pop('FIRECRAWL_API_KEY', None)  # Temporarily remove
    resolved = test_server.get_resolved_url()
    if resolved:
        print(f"⚠️  URL resolved without API key: {resolved}")
    else:
        print("✅ Correctly failed when API key missing")
    
    # Restore API key
    api_key = input("\nPlease enter your FIRECRAWL_API_KEY for testing (or press Enter to skip): ").strip()
    if api_key:
        os.environ['FIRECRAWL_API_KEY'] = api_key


def show_usage():
    """Show usage instructions."""
    print("🚀 Firecrawl Hosted MCP Server Test")
    print("=" * 40)
    print()
    print("This script tests the integration with Firecrawl's hosted MCP server.")
    print()
    print("Prerequisites:")
    print("1. Get a Firecrawl API key from: https://firecrawl.dev/app/api-keys")
    print("2. Set the environment variable: export FIRECRAWL_API_KEY=your_api_key")
    print()
    print("Usage:")
    print("   export FIRECRAWL_API_KEY=fc-your-api-key")
    print("   python test_firecrawl_hosted.py")
    print()


async def main():
    """Main test function."""
    show_usage()
    
    # Test URL resolution first
    await test_url_resolution()
    
    # Test the full integration
    success = await test_firecrawl_hosted()
    
    if success:
        print("\n✅ All tests passed!")
        print("\nNext steps:")
        print("1. Add Firecrawl server to chat_term with unified MCP provider")
        print("2. Test other third-party MCP servers with similar URL patterns")
        print("3. Add server management commands to chat_term")
    else:
        print("\n❌ Some tests failed. Please check the configuration and try again.")
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
