#!/usr/bin/env python3
"""
Simple test to check if the Django chat API is working.
"""

import requests
import json

def test_simple_chat():
    """Test basic chat functionality."""
    
    print("Testing Django Chat API")
    print("=" * 30)
    
    base_url = "http://localhost:8000"
    
    # Test the regular chat endpoint
    chat_url = f"{base_url}/gaia_chat/api/chat/"
    
    test_data = {
        "message": "hello",
        "conversation_id": "test_conversation",
        "provider": "mock"
    }
    
    try:
        print(f"Sending POST request to: {chat_url}")
        print(f"Data: {json.dumps(test_data, indent=2)}")
        
        response = requests.post(chat_url, json=test_data, timeout=30)
        
        print(f"Response status: {response.status_code}")
        print(f"Response: {response.text}")
        
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")

if __name__ == "__main__":
    test_simple_chat()
