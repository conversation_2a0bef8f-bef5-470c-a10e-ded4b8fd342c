#!/usr/bin/env python3
"""
Test script to verify that the Django chat app shows incremental progress in browser console.

This script tests the enhanced Django API functionality that detects direct tool calls
and displays progress updates in real-time in the browser console, similar to chat_term.py.
"""

import requests
import json
import time

def test_browser_progress():
    """Test that progress messages appear in browser console when calling long_task."""
    
    print("Testing Browser Console Progress Display")
    print("=" * 50)
    print()
    print("This test will:")
    print("1. Send a 'long_task' message to the Django chat API")
    print("2. Stream the response to see progress events")
    print("3. Verify that progress events include console_message field")
    print()
    print("To see the progress in browser console:")
    print("1. Open http://localhost:8000/gaia_chat/ in your browser")
    print("2. Open browser developer tools (F12)")
    print("3. Go to Console tab")
    print("4. Type 'long_task' in the chat input and send")
    print("5. Watch for progress messages in the console")
    print()
    print("Expected console output format:")
    print("📊 [████████████████████████████] 100.0% 1/5 – //// CONTEXT PROGRESS - CUSTOM MESSAGE 1 ////")
    print()
    
    # Test the API directly to verify the response format
    base_url = "http://localhost:8000"
    
    # First, we need to authenticate (this is a simplified test)
    print("Testing API response format...")
    print("-" * 30)
    
    # Test the streaming endpoint directly
    stream_url = f"{base_url}/gaia_chat/api/chat/stream/"
    
    # Create test data
    test_data = {
        "message": "long_task",
        "conversation_id": "test_conversation",
        "provider": "mcp-http"
    }
    
    try:
        print(f"Sending POST request to: {stream_url}")
        print(f"Data: {json.dumps(test_data, indent=2)}")
        print()
        
        # Note: This will fail without authentication, but we can see the structure
        response = requests.post(
            stream_url,
            json=test_data,
            stream=True,
            headers={'Accept': 'text/event-stream'},
            timeout=30
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("Streaming response events:")
            print("-" * 25)
            
            event_count = 0
            progress_events = []
            
            for line in response.iter_lines(decode_unicode=True):
                if line and line.startswith('data: '):
                    try:
                        event_data = json.loads(line[6:])  # Remove 'data: ' prefix
                        event_count += 1
                        
                        print(f"Event {event_count}: {event_data.get('type', 'unknown')}")
                        
                        if event_data.get('type') == 'progress':
                            progress_events.append(event_data)
                            
                            # Check if console_message is present
                            if 'console_message' in event_data:
                                print(f"  ✅ Console message: {event_data['console_message']}")
                            else:
                                print(f"  ❌ Missing console_message field")
                                print(f"     Event data: {json.dumps(event_data, indent=4)}")
                        
                        # Stop after a reasonable number of events
                        if event_count >= 20:
                            break
                            
                    except json.JSONDecodeError as e:
                        print(f"  ❌ Failed to parse JSON: {e}")
                        print(f"     Raw line: {line}")
            
            print()
            print(f"Summary:")
            print(f"  Total events: {event_count}")
            print(f"  Progress events: {len(progress_events)}")
            
            if progress_events:
                print(f"  ✅ Progress events found with console messages!")
                for i, event in enumerate(progress_events, 1):
                    if 'console_message' in event:
                        print(f"    {i}. {event['console_message']}")
            else:
                print(f"  ❌ No progress events found")
                
        else:
            print(f"Error response: {response.text}")
            if response.status_code == 403:
                print()
                print("Note: Authentication required for full test.")
                print("Please test manually in the browser:")
                print("1. Go to http://localhost:8000/gaia_chat/")
                print("2. Log in if needed")
                print("3. Type 'long_task' and send")
                print("4. Check browser console for progress messages")
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        print()
        print("Make sure:")
        print("1. Django server is running on localhost:8000")
        print("2. MCP HTTP server is running on localhost:9000")
        print()
        print("To start Django server:")
        print("  cd gaia/djangaia && python manage.py runserver")
        print()
        print("To start MCP server:")
        print("  cd gaia/gaia_ceto/proto_mcp_http && python mcp_http_server.py")

if __name__ == "__main__":
    test_browser_progress()
